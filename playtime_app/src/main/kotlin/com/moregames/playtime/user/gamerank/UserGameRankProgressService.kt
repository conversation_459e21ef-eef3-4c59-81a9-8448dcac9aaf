package com.moregames.playtime.user.gamerank

import com.google.inject.Singleton
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.logger
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.user.challenge.progress.achievement.AchievementDto
import com.moregames.playtime.user.gamerank.calculators.GameRankProgressCalculator
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import javax.inject.Inject

@Singleton
class UserGameRankProgressService @Inject constructor(
  private val gamesService: GamesService,
  private val userGameRankPersistenceService: UserGameRankPersistenceService,
  private val calculators: Map<GameRankCalculator, GameRankProgressCalculator>,
  private val json: Json,
) {
  suspend fun handleUserProgress(message: UserChallengeProgressDto) {
    val gameId = gamesService.getGameId(message.applicationId, AppPlatform.ANDROID) ?: return
    val config = GameRankConfigHolder.gamesConfig[gameId] ?: return
    val calculator = calculators[config.calculator] ?: return
    val currentProgress = userGameRankPersistenceService.getUserGameRankEntity(message.userId, gameId)
    val updatedProgress = calculator.calculateProgress(message, currentProgress.progress, parseAchievement(currentProgress.achievement), config)
    if (updatedProgress.rank != currentProgress.rank) {
      // TODO send push notification
    }
    userGameRankPersistenceService.saveUserGameRank(
      currentProgress.copy(
        rank = updatedProgress.rank,
        progress = updatedProgress.progress,
        achievement = updatedProgress.achievement?.asString(),
      )
    )
  }

  private fun parseAchievement(achievementAsString: String?): AchievementDto? {
    if (achievementAsString.isNullOrBlank()) return null

    return try {
      json.decodeFromString(achievementAsString)
    } catch (e: Exception) {
      logger().error("Error parsing Achievement string", e)
      return null
    }
  }

  private fun AchievementDto.asString(): String {
    return json.encodeToString(this)
  }
}